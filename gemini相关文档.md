## 基本的 api 调用

```
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent" \
  -H 'Content-Type: application/json' \
  -H 'X-goog-api-key: GEMINI_API_KEY' \
  -X POST \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Explain how AI works in a few words"
          }
        ]
      }
    ]
  }'
```





## 理解图片的api

```
IMG_PATH="/path/to/your/image1.jpg"

if [[ "$(base64 --version 2>&1)" = *"FreeBSD"* ]]; then
B64FLAGS="--input"
else
B64FLAGS="-w0"
fi

curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent" \
-H "x-goog-api-key: $GEMINI_API_KEY" \
-H 'Content-Type: application/json' \
-X POST \
-d '{
    "contents": [{
    "parts":[
        {
            "inline_data": {
            "mime_type":"image/jpeg",
            "data": "'"$(base64 $B64FLAGS $IMG_PATH)"'"
            }
        },
        {"text": "Caption this image."},
    ]
    }]
}' 2> /dev/null
```



## 参考实现的提示词

```
// gemini版本提示词

要实现你的需求（调用 Google Gemini 模型，上传 Base64 编码的图像，包含用户输入的 API key 和默认值，添加指定的提示词，并解析返回的 JSON 响应以提取 `Tags` 和 `Description`），我们需要在 SwiftUI 中调整 `APIService` 和相关代码，适配 Gemini API 的请求格式和你的提示词要求。以下是为 AI IDE（如 Xcode 或其他代码生成工具）设计的提示词，包含主要文件、关键代码分析和实现建议，基于 Apple 文档和你的场景。

### AI IDE 提示词
**任务描述**：
开发一个 SwiftUI 应用，调用 Google Gemini API（`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`），上传 Base64 编码的图像，处理用户输入的 API key（开发阶段提供默认值），并使用指定的提示词生成 JSON 响应，包含 `Tags`（字符串数组）和 `Description`（字符串）。解析响应并在 UI 中显示结果。确保代码模块化、安全，并符合 Apple 最佳实践。

**功能要求**：
1. **API key 管理**：
   - 用户可通过界面输入 API key，存储到 Keychain。
   - 开发阶段提供默认 API key（`YOUR_DEFAULT_API_KEY`），仅用于测试。
   - 使用 `KeychainAccess` 库（SPM 依赖）存储和读取 API key。
2. **图像上传**：
   - 接受 `UIImage`（从 `UIImagePickerController` 或本地文件获取），转换为 Base64。
   - 发送 POST 请求，JSON body 包含 Base64 图像和提示词。
3. **提示词**：
   - 包含以下提示词，嵌入 JSON 请求的 `text` 字段：
     ```
     # ROLE
     You are an expert in image content recognition and analysis.

     # GOAL
     Your task is to receive an input image that contains a main subject, identify its distinct components, and output both:
     1. A list of clearly identifiable component tags.
     2. A concise one-sentence description of the overall subject.

     # EXAMPLE
     ## User's Request
     {
       "image": "{{...}}",
     }
     ## Your output
     {
       "Tags": ["塑料瓶身", "纸质杯托"],
       "Description": "这是一个赛百味品牌的杯子"
     }

     # NOTICE
     - List only the most obvious, visually distinct components in the Tags array.
     - Keep the Description concise and informative.
     - Only output valid JSON with exactly the keys: "Tags" and "Description".
     - Do not include additional commentary, explanation, or other keys.

     Now, based on the provided image input, perform the recognition and output the result in the requested JSON format.
     ```
4. **响应解析**：
   - 解析 Gemini API 响应，提取 `Tags`（`[String]`）和 `Description`（`String`）。
   - 预期响应格式：`{"Tags": ["粉色头发", "绿色眼睛", "黑色发饰", "白色衣领"], "Description": "这是一张动漫角色阿尼亚·福杰的特写插画。"}`。
5. **UI**：
   - 显示加载状态、错误信息、解析后的 `Tags` 和 `Description`。
   - 提供输入框设置 API key 和按钮上传图像。
6. **安全与测试**：
   - 使用 Keychain 存储 API key，避免硬编码。
   - 默认 API key 仅用于开发，生产中提示用户输入。
   - 错误处理：无效 URL、HTTP 状态码、JSON 解码失败。

**主要文件**：
1. **APIService.swift**：包含 `APIService` 类，处理 API key 存储、图像上传、请求发送和响应解析。
2. **ContentView.swift**：SwiftUI 视图，包含 API key 输入、图像选择、上传按钮和结果显示。
3. **Dependencies**：
   - `KeychainAccess`（SPM：`https://github.com/kishikawakatsumi/KeychainAccess`）。

**关键代码分析**：
- **请求模型**（`GeminiRequest`）：
  - 结构：`contents.parts` 包含两部分：Base64 图像（`inline_data`）和提示词（`text`）。
  - 使用 `Encodable` 确保 JSON 编码正确，匹配 Gemini API 的格式：
    ```json
    {
      "contents": [{
        "parts": [
          {"inline_data": {"mime_type": "image/jpeg", "data": "<base64>"}},
          {"text": "<提示词>"}
        ]
      }]
    }
    ```
- **响应模型**（`GeminiResponse`）：
  - 解析 Gemini API 的响应，提取 `candidates.content.parts.text`，预期为 JSON 字符串。
  - 使用 `JSONDecoder` 二次解码 `text` 字段为 `{"Tags": [String], "Description": String}`。
- **API key**：
  - 开发阶段可以硬编码默认值 `YOUR_DEFAULT_API_KEY`  为AIzaSyCej2y_ixfxI0ZxkIKSKuoSq0m7wtoJ7XE
  - 但是用户通过 `TextField` 输入新 key，调用 `setupAPIKey` 存储（到本地）
- **图像处理**：
  - `UIImage` 转为 `Data`（JPEG，压缩质量 0.8），使用 `base64EncodedString()` 编码。
- **UI 更新**：
  - 使用 `@Published` 属性（`tags`, `description`, `isLoading`, `errorMessage`）触发 SwiftUI 视图更新。
  - `@MainActor` 确保线程安全。

**代码实现**：
import Foundation
import SwiftUI

// 请求模型
struct GeminiRequest: Encodable {
    struct Content: Encodable {
        struct Part: Encodable {
            struct InlineData: Encodable {
                let mime_type: String
                let data: String
            }
            let inline_data: InlineData?
            let text: String?
        }
        let parts: [Part]
    }
    let contents: [Content]
}

// 响应模型
struct GeminiResponse: Decodable {
    struct Candidate: Decodable {
        struct Content: Decodable {
            struct Part: Decodable {
                let text: String
            }
            let parts: [Part]
        }
        let content: Content
    }
    let candidates: [Candidate]
}

// 解析的输出模型
struct ImageAnalysisResult: Decodable {
    let Tags: [String]
    let Description: String
}

@MainActor
class APIService: ObservableObject {
    @Published var tags: [String] = []
    @Published var description: String = ""
    @Published var isLoading = false
    @Published var errorMessage: String? = nil
    @Published var apiKeyInput: String = ""
    
    private let keychain = Keychain(service: "com.yourapp.apiservice")
    private let apiKeyKey = "gemini_api_key"
    private let defaultAPIKey = "YOUR_DEFAULT_API_KEY" // 仅开发阶段使用
    
    // 初始化时检查或设置默认 API key
    init() {
        if (try? keychain.get(apiKeyKey)) == nil {
            setupAPIKey(defaultAPIKey)
        }
    }
    
    // 存储 API key
    func setupAPIKey(_ key: String) {
        do {
            try keychain.set(key, key: apiKeyKey)
        } catch {
            errorMessage = "Failed to save API key: \(error.localizedDescription)"
        }
    }
    
    func uploadImage(_ imageData: Data) async {
        isLoading = true
        errorMessage = nil
        
        guard let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent") else {
            errorMessage = "Invalid URL"
            isLoading = false
            return
        }
        
        guard let apiKey = try? keychain.get(apiKeyKey) else {
            errorMessage = "API key not found"
            isLoading = false
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "x-goog-api-key")
        
        // 提示词
        let prompt = """
        # ROLE
        You are an expert in image content recognition and analysis.

        # GOAL
        Your task is to receive an input image that contains a main subject, identify its distinct components, and output both:
        1. A list of clearly identifiable component tags.
        2. A concise one-sentence description of the overall subject.

        # EXAMPLE
        ## User's Request
        {
          "image": "{{...}}",
        }
        ## Your output
        {
          "Tags": ["塑料瓶身", "纸质杯托"],
          "Description": "这是一个赛百味品牌的杯子"
        }

        # NOTICE
        - List only the most obvious, visually distinct components in the Tags array.
        - Keep the Description concise and informative.
        - Only output valid JSON with exactly the keys: "Tags" and "Description".
        - Do not include additional commentary, explanation, or other keys.

        Now, based on the provided image input, perform the recognition and output the result in the requested JSON format.
        """
        
        // Base64 编码图像
        let base64String = imageData.base64EncodedString()
        let requestBody = GeminiRequest(
            contents: [
                .init(parts: [
                    .init(inline_data: .init(mime_type: "image/jpeg", data: base64String), text: nil),
                    .init(inline_data: nil, text: prompt)
                ])
            ]
        )
        
        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            errorMessage = "Encoding error: \(error.localizedDescription)"
            isLoading = false
            return
        }
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                errorMessage = "Server error or invalid response"
                isLoading = false
                return
            }
            
            let decodedResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)
            if let jsonString = decodedResponse.candidates.first?.content.parts.first?.text {
                // 二次解码 JSON 字符串
                if let jsonData = jsonString.data(using: .utf8),
                   let result = try? JSONDecoder().decode(ImageAnalysisResult.self, from: jsonData) {
                    tags = result.Tags
                    description = result.Description
                    print("Tags: \(tags), Description: \(description)")
                } else {
                    errorMessage = "Failed to parse JSON response"
                }
            } else {
                errorMessage = "No response text found"
            }
            
            isLoading = false
        } catch {
            errorMessage = "Failed to upload image: \(error.localizedDescription)"
            isLoading = false
        }
    }
}



// 图像选择器（已经在我的项目中实现）
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var image: UIImage?
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let uiImage = info[.originalImage] as? UIImage {
                parent.image = uiImage
            }
            picker.dismiss(animated: true)
        }
    }
}


### 关键代码分析
1. **API key 管理**：
   - 默认值 `YOUR_DEFAULT_API_KEY` 在 `APIService.init` 中设置到 Keychain（仅首次）。
   - 用户通过 `TextField` 输入新 key，调用 `setupAPIKey` 更新。
   - Keychain 确保安全存储，符合 Apple 文档（Keychain Services）。
2. **请求构建**：
   - `GeminiRequest` 匹配 Gemini API 的 JSON 结构，包含 Base64 图像和提示词。
   - 提示词嵌入 `text` 字段，硬编码在代码中（可改为从文件加载）。
3. **响应解析**：
   - `GeminiResponse` 提取 `text`（JSON 字符串），二次解码为 `ImageAnalysisResult`。
   - 确保输出匹配 `{"Tags": [String], "Description": String}`。
4. **图像处理**：
   - `imageData.base64EncodedString()` 生成 Base64，等效于 `curl` 的 `base64 -w0`。
   - JPEG 压缩（0.8）平衡质量和大小。
5. **UI**：
   - `TextField` 允许用户输入 API key。
   - `ImagePicker` 使用 `UIImagePickerController` 选择图像。
   - 按钮触发上传，显示 `tags` 和 `description`。
6. **错误处理**：
   - 检查 URL、HTTP 状态码、JSON 解码错误。
   - 显示 `errorMessage` 在 UI 中。

### 实现建议
4. **优化**：
   - 可将提示词存储在 `.plist` 或单独文件中，运行时加载。
   - 添加取消请求功能（`URLSessionTask.cancel()`）。

### 提示词总结
此提示词为 AI IDE 提供了清晰的结构，涵盖文件、模型、逻辑和 UI，确保实现与 `curl` 示例一致，同时适配 SwiftUI 和 Gemini API。代码模块化，安全（Keychain），并支持用户输入 API key 和默认值，满足开发和测试需求。
```

