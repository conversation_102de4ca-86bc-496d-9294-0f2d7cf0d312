import Foundation
import Combine
import UIKit

class CarbonPetViewModel: ObservableObject {
    // MARK: 发布的数据
    @Published var petTemplates: [PetTemplate] = []
    @Published var userPets: [UserPet] = []
    @Published var displayModels: [PetDisplayModel] = []
    @Published var isLoading = false

    // 本地存储 Key
    private let userPetsKey = "user_pets_v2"

    init() {
        loadPetTemplates()
    }

    // MARK: 加载宠物图鉴模板
    func loadPetTemplates() {
        guard let url = Bundle.main.url(forResource: "pets", withExtension: "json"),
              let data = try? Data(contentsOf: url) else {
            print("Failed to find or load pets.json")
            return
        }

        let decoder = JSONDecoder()
        do {
            let templates = try decoder.decode([PetTemplate].self, from: data)
            
            print("Loading pets...")
            DispatchQueue.main.async {
                self.petTemplates = templates
                self.loadUserPets()
                self.updateDisplayModels()
            }
        } catch {
            print("Failed to decode pets.json: \\(error)")
        }
    }

    // MARK: 加载用户已获得宠物
    func loadUserPets() {
        print("Loading user's pets...")
        if let data = UserDefaults.standard.data(forKey: userPetsKey),
           let pets = try? JSONDecoder().decode([UserPet].self, from: data) {
            userPets = pets
        } else {
            print("开发阶段获得默认宠物")
            // 开发阶段：自动解锁第一只宠物 和 最后一只 作为 Mock 数据
            var mockPets: [UserPet] = []
            guard !petTemplates.isEmpty else{
                userPets = mockPets
                return
            }
            
            if let firstTemplate = petTemplates.first{
                let firstUserPet = UserPet(templateName: firstTemplate.name)
                mockPets.append(firstUserPet)
            }
            
            if let lastTemplate = petTemplates.last{
                let lastUserPet = UserPet(templateName: lastTemplate.name)
                mockPets.append(lastUserPet)
            }
            
            userPets = mockPets
            saveUserPets()
        }
    }

    // MARK: 保存用户宠物数据
    func saveUserPets() {
        if let data = try? JSONEncoder().encode(userPets) {
            UserDefaults.standard.set(data, forKey: userPetsKey)
        }
    }

    /// 更新显示模型
    func updateDisplayModels() {
        displayModels = petTemplates.map { template in
            let userPet = userPets.first { $0.templateName == template.name }
            return PetDisplayModel(template: template, userPet: userPet)
        }
    }

    /// 喂养宠物
    func feedPet(userPetId: UUID, experience: Int = 10) {
        if let index = userPets.firstIndex(where: { $0.id == userPetId }) {
            userPets[index].feed(experience: experience)
            if userPets[index].canLevelUp {
                userPets[index].levelUp()
            }
            saveUserPets()
            updateDisplayModels()
        }
    }

    /// 解锁新宠物（通过模板名称）
    func unlockPet(templateName: String) {
        guard petTemplates.contains(where: { $0.name == templateName }),
              !userPets.contains(where: { $0.templateName == templateName }) else {
            return
        }

        let newUserPet = UserPet(templateName: templateName)
        userPets.append(newUserPet)
        saveUserPets()
        updateDisplayModels()
    }

    /// 检查是否可以购买宠物
    func canPurchasePet(templateName: String, currentAmount: Int) -> Bool {
        guard let template = petTemplates.first(where: { $0.name == templateName }),
              !isPetOwned(templateName: templateName) else {
            return false
        }

        switch template.acquisition {
        case .purchase(let amount):
            return currentAmount >= amount
        case .loginDays(_), .task(_):
            // TODO: 实现其他获得条件的检查
            return false
        }
    }

    /// 购买宠物
    func purchasePet(templateName: String, currentAmount: Int) -> Bool {
        guard canPurchasePet(templateName: templateName, currentAmount: currentAmount),
              let template = petTemplates.first(where: { $0.name == templateName }) else {
            return false
        }

        switch template.acquisition {
        case .purchase(_):
            // 解锁宠物（金币扣除在调用方处理）
            unlockPet(templateName: templateName)
            return true
        case .loginDays(_), .task(_):
            // TODO: 实现其他获得方式
            return false
        }
    }

    /// 检查用户是否拥有特定宠物
    func isPetOwned(templateName: String) -> Bool {
        return userPets.contains(where: { $0.templateName == templateName })
    }

    /// 根据模板名称获取用户宠物
    func getUserPet(templateName: String) -> UserPet? {
        return userPets.first(where: { $0.templateName == templateName })
    }

    /// 根据模板名称获取宠物模板
    func getPetTemplate(name: String) -> PetTemplate? {
        return petTemplates.first(where: { $0.name == name })
    }

    /// 获取宠物图像（从 Assets）
    func getPetImage(imageName: String) -> UIImage? {
        return UIImage(named: imageName)
    }

    /// 根据ID获取宠物显示模型
    func getDisplayModel(by id: UUID) -> PetDisplayModel? {
        return displayModels.first(where: { $0.id == id })
    }

    /// 根据模板名称获取宠物显示模型
    func getDisplayModel(by templateName: String) -> PetDisplayModel? {
        return displayModels.first(where: { $0.template.name == templateName })
    }

    /// 获取宠物的详细信息（用于详情页面）
    func getPetDetailInfo(for displayModel: PetDisplayModel) -> (template: PetTemplate, userPet: UserPet?, isOwned: Bool) {
        return (
            template: displayModel.template,
            userPet: displayModel.userPet,
            isOwned: displayModel.isOwned
        )
    }

    /// 获取宠物的装扮信息（占位方法，后续扩展）
    func getPetOutfits(for templateName: String) -> [String] {
        // 暂时返回占位数据
        return ["礼冠套装", "花冠套装", "魔法师套装"]
    }

    /// 获取有序的模型显示列表
    func getOrderedDisplayModels() -> [PetDisplayModel] {
        let ownedModels = displayModels.filter({ $0.isOwned })
        let unownedModels = displayModels.filter({ !$0.isOwned})
        return ownedModels + unownedModels
    }
    
    /// 切换模型
    func getNextDisplayModel(current: PetDisplayModel) -> PetDisplayModel?{
        let orderedModels = getOrderedDisplayModels()
        guard let currentIndex = orderedModels.firstIndex(where: {$0.id == current.id})
        else
        {
            return nil
        }
        let nextIndex = (currentIndex + 1) % orderedModels.count
        return orderedModels[nextIndex]
    }
    
    func getPreviousDisplayModel(current: PetDisplayModel) -> PetDisplayModel?{
        let orderedModels = getOrderedDisplayModels()
        guard let currentIndex = orderedModels.firstIndex(where: {$0.id == current.id})
        else{
            return nil
        }
        
        let previousIndex = currentIndex == 0 ? orderedModels.count - 1: currentIndex - 1
        return orderedModels[previousIndex]
    }
}
