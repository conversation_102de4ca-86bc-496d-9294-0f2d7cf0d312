//
//  PetAcquisitionSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/25.
//

import SwiftUI

struct PetAcquisitionSheet: View {
    let displayModel: PetDisplayModel
    let currentAmount: Int
    let onPurchase: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: Theme.Spacing.lg) {
                // 宠物灰色图像
                petImageSection

                // 宠物信息
                petInfoSection

                // 获得条件
                acquisitionConditionSection

                Spacer()

                // 获得宠物按钮
                acquisitionButton
            }
            .padding(Theme.Spacing.lg)
            .navigationTitle("获得宠物")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: 宠物图像区域
    private var petImageSection: some View {
        ZStack {
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(LinearGradient.petItemBgColor)
                .frame(width: 200, height: 200)
                .shadow(color: .black.opacity(0.3), radius: 10, y: 5)

            if let petImage = UIImage(named: displayModel.template.imageName) {
                Image(uiImage: petImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 180, height: 180)
                    .colorMultiply(.black.opacity(0.5)) // 灰色效果
            }
        }
        .glassCard()
    }

    // MARK: 宠物信息区域
    private var petInfoSection: some View {
        VStack(spacing: Theme.Spacing.md) {
            Text(displayModel.template.name)
                .font(.title2Brand)
                .foregroundColor(.textPrimary)

            HStack(spacing: 2) {
                ForEach(0..<displayModel.template.rarity, id: \.self) { _ in
                    Image("star")
                        .foregroundColor(.yellow)
                        .font(.system(size: 16))
                }
            }

            Text(displayModel.template.introduction)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .padding()
        .glassCard()
    }

    // MARK: 获得条件区域
    private var acquisitionConditionSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("获得条件")
                .font(.headline)
                .foregroundColor(.textPrimary)

            switch displayModel.template.acquisition {
            case .purchase(let amount):
                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.brandGreen)
                    Text("需要碳币: \(amount)")
                        .font(.bodyBrand)
                    Spacer()
                    Text("当前: \(currentAmount)")
                        .font(.bodyBrand)
                        .foregroundColor(currentAmount >= amount ? .success : .error)
                }

            case .loginDays(let days):
                HStack {
                    Image(systemName: "calendar.badge.clock")
                        .foregroundColor(.info)
                    Text("需要连续登录: \(days) 天")
                        .font(.bodyBrand)
                    Spacer()
                    // TODO: 实现登录天数检查
                    Text("未实现")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }

            case .task(let taskId):
                HStack {
                    Image(systemName: "checkmark.circle")
                        .foregroundColor(.brandColor)
                    Text("完成指定任务: \(taskId)")
                        .font(.bodyBrand)
                    Spacer()
                    // TODO: 实现任务完成检查
                    Text("未实现")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .padding()
        .background(Color.cardBackground.opacity(0.3))
        .cornerRadius(Theme.CornerRadius.md)
    }

    // MARK: 获得宠物按钮
    private var acquisitionButton: some View {
        Button(action: {
            switch displayModel.template.acquisition {
            case .purchase(_):
                onPurchase()
            case .loginDays(_), .task(_):
                // TODO: 实现其他获得方式
                break
            }
        }) {
            HStack {
                Image(systemName: "heart.fill")
                Text("获得宠物")
                    .font(.bodyBrand)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                canAcquire ? Color.brandGreen : Color.gray
            )
            .cornerRadius(Theme.CornerRadius.md)
        }
        .disabled(!canAcquire)
    }

    // 是否可以获得
    private var canAcquire: Bool {
        switch displayModel.template.acquisition {
        case .purchase(let amount):
            return currentAmount >= amount
        case .loginDays(_), .task(_):
            // TODO: 实现其他条件检查
            return false
        }
    }
}
