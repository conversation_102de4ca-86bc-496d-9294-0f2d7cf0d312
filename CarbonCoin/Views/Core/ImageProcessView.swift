//
//  ImageProcessView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import SwiftUI
import PhotosUI
import Photos

struct ImageProcessView: View {
    @StateObject private var viewModel = ImageProcessViewModel()
    @EnvironmentObject var cardStore: CardStore
    @State private var showCamera = false
    @State private var showCardCreatedAlert = false
    @State private var isCroppingProcessed = false
    @State private var isCroppingInput = false
    @StateObject private var locationManager = UserLocationManager()

    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                // 图片显示区域
                imageDisplayArea

                // 提示文本和控制按钮
                if viewModel.inputImage != nil && viewModel.processedImage == nil {
                    VStack(spacing: 12) {
                        Text(getInstructionText())
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        // 选择控制按钮
                        if viewModel.showSubjectSelection && !viewModel.subjects.isEmpty {
                            HStack(spacing: 16) {
                                Button("全选") {
                                    viewModel.selectAllSubjects()
                                }
                                .font(.caption)
                                .foregroundColor(.blue)

                                Button("清除") {
                                    viewModel.clearSelection()
                                }
                                .font(.caption)
                                .foregroundColor(.red)

                                Text("已选择: \(viewModel.selectedSubjectIndices.count)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }

                // 操作按钮
                HStack(spacing: 20) {
                    importButton
                    processButton
                    saveButton
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)

                // 图像分析结果显示区域
                if viewModel.processedImage != nil {
                    imageAnalysisResultView
                }

                Spacer(minLength: Theme.Spacing.tab)
            }
            .padding()
            .navigationTitle("主体提取")
            .sheet(isPresented: $showCamera) {
                ImagePicker(selectedImage: $viewModel.inputImage)
            }
            .photosPicker(isPresented: $viewModel.showPhotoPicker, selection: $viewModel.selectedPhoto, matching: .images)
            .alert("保存成功", isPresented: $viewModel.showSaveAlert) {
                Button("确定", role: .cancel) { }
            }
            .alert("卡片创建成功", isPresented: $showCardCreatedAlert) {
                Button("确定", role: .cancel) { }
            } message: {
                Text("物品卡片已保存到卡片库中")
            }
            .alert("需要相册权限", isPresented: $viewModel.showPermissionAlert) {
                Button("前往设置") {
                    if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsURL)
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("请在设置中授予相册访问权限以选择图片")
            }
            .overlay {
                if viewModel.isProcessing {
                    ProgressView()
                        .scaleEffect(2)
                }
            }
            // 监听 selectedPhoto 变化以触发加载
            .onChange(of: viewModel.selectedPhoto) {
                Task {
                    await viewModel.loadImage()
                }
            }
            // 监听 inputImage 变化以重新检测主体（用于图像编辑后）
            .onChange(of: viewModel.inputImage) {
                // 使用 DispatchQueue.main.async 确保在主线程中执行
                DispatchQueue.main.async {
                    viewModel.reDetectSubjects()
                }
            }
        }
    }

    // MARK: - 创建卡片方法
    private func createItemCard() {
        guard let result = viewModel.analysisResult,
              let processedImage = viewModel.processedImage,
              let imageData = processedImage.pngData() else {
            return
        }

        // 先立即创建卡片，不等待位置信息
        let cardId = UUID()
        let _ = cardStore.saveCard(
            tags: result.Tags,
            description: result.Description,
            title: result.Title,
            imageData: imageData,
            location: "获取位置中...",
            latitude: nil,
            longitude: nil,
            remark: nil,
            imageURL: "",
            cardId: cardId
        )

        // 显示成功提示
        showCardCreatedAlert = true

        // 异步获取位置信息并更新卡片
        Task {
            await updateCardLocation(cardId: cardId)
        }
    }

    // 异步更新卡片位置信息
    @MainActor
    private func updateCardLocation(cardId: UUID) async {
        // 使用现有位置信息或异步获取新位置
        let (location, locationString) = await getLocationInfoSafely()

        // 更新卡片位置信息
        if let cardIndex = cardStore.cards.firstIndex(where: { $0.id == cardId }) {
            let existingCard = cardStore.cards[cardIndex]
            let updatedCard = ItemCard(
                id: existingCard.id,
                tags: existingCard.tags,
                description: existingCard.description,
                title: existingCard.title,
                imageFileName: existingCard.imageFileName,
                imageURL: existingCard.imageURL,
                createdAt: existingCard.createdAt,
                authorId: existingCard.authorId,
                location: locationString,
                latitude: location?.coordinate.latitude,
                longitude: location?.coordinate.longitude,
                remark: existingCard.remark
            )
            cardStore.cards[cardIndex] = updatedCard
            cardStore.saveCards()
        }
    }

    // 安全获取位置信息，不阻塞UI
    private func getLocationInfoSafely() async -> (CLLocation?, String) {
        // 如果已有位置信息，直接使用
        if let currentLoc = locationManager.currentLocation,
           let currentLocString = locationManager.currentLocationString {
            return (currentLoc, currentLocString)
        }

        // 检查权限状态
        guard locationManager.authorizationStatus.isAuthorized else {
            return (nil, "位置权限未授权")
        }

        // 尝试获取位置，但设置超时
        return await withTaskGroup(of: (CLLocation?, String).self) { group in
            // 添加位置获取任务
            group.addTask {
                await self.locationManager.getCurrentLocationInfo()
            }

            // 添加超时任务
            group.addTask {
                try? await Task.sleep(nanoseconds: 3_000_000_000) // 3秒超时
                return (nil, "位置获取超时")
            }

            // 返回第一个完成的结果
            if let result = await group.next() {
                group.cancelAll()
                return result
            }

            return (nil, "位置获取失败")
        }
    }

    // MARK: - 子视图
    private var imageDisplayArea: some View {
        GeometryReader { geometry in
            Group {
                VStack {
                    if let processedImage = viewModel.processedImage {
                        // 显示处理后的图片，支持编辑
                        Image(uiImage: processedImage)
                            .resizable()
                            .scaledToFit()
                            .frame(maxHeight: 400)
                            .croppable($isCroppingProcessed, image: Binding(
                                get: { viewModel.processedImage },
                                set: { newImage in
                                    viewModel.processedImage = newImage
                                }
                            ))
                    } else if let inputImage = viewModel.inputImage {
                        // 显示原始图片，支持点击选择主体和编辑
                        ZStack {
                            Image(uiImage: inputImage)
                                .resizable()
                                .scaledToFit()
                                .frame(maxHeight: 400)
                                .croppable($isCroppingInput, image: Binding(
                                    get: { viewModel.inputImage },
                                    set: { newImage in
                                        viewModel.inputImage = newImage
                                    }
                                ))
                                .onTapGesture { location in
                                    // 将点击位置转换为标准化坐标 (0-1)
                                    let imageFrame = getImageFrame(in: geometry, for: inputImage)
                                    let normalizedPoint = CGPoint(
                                        x: (location.x - imageFrame.minX) / imageFrame.width,
                                        y: (location.y - imageFrame.minY) / imageFrame.height
                                    )
                                    viewModel.handleImageTap(at: normalizedPoint)
                                }

                            // 显示所有主体的选择指示器
                            if viewModel.showSubjectSelection {
                                let imageFrame = getImageFrame(in: geometry, for: inputImage)
                                ForEach(viewModel.subjects, id: \.index) { subject in
                                    SubjectIndicator(
                                        subject: subject,
                                        isSelected: viewModel.selectedSubjectIndices.contains(subject.index),
                                        imageFrame: imageFrame
                                    ) {
                                        viewModel.toggleSubjectSelection(subject.index)
                                    }
                                }
                            }
                        }
                    } else {
                        Image(systemName: "photo.on.rectangle")
                            .font(.system(size: 100))
                            .foregroundColor(.gray)
                            .frame(maxHeight: 400)
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .background(Color(.systemGroupedBackground))
            .cornerRadius(12)
        }
    }

    // MARK: - 辅助方法
    private func getImageFrame(in geometry: GeometryProxy, for image: UIImage) -> CGRect {
        let imageAspectRatio = image.size.width / image.size.height
        let containerAspectRatio = geometry.size.width / geometry.size.height

        let imageSize: CGSize
        if imageAspectRatio > containerAspectRatio {
            // 图片更宽，以宽度为准
            imageSize = CGSize(
                width: geometry.size.width,
                height: geometry.size.width / imageAspectRatio
            )
        } else {
            // 图片更高，以高度为准
            let maxHeight: CGFloat = 400
            let actualHeight = min(maxHeight, geometry.size.height)
            imageSize = CGSize(
                width: actualHeight * imageAspectRatio,
                height: actualHeight
            )
        }

        return CGRect(
            x: (geometry.size.width - imageSize.width) / 2,
            y: (geometry.size.height - imageSize.height) / 2,
            width: imageSize.width,
            height: imageSize.height
        )
    }

    private var importButton: some View {
        Menu {
            Button {
                Task {
                    if await viewModel.checkPhotoLibraryPermission() {
                        showCamera = true
                    }
                }
            } label: {
                Label("拍照", systemImage: "camera")
            }

            Button {
                Task {
                    if await viewModel.checkPhotoLibraryPermission() {
                        viewModel.showPhotoPicker = true
                    }
                }
            } label: {
                Label("从相册选择", systemImage: "photo")
            }
        } label: {
            Text("导入图片")
                .frame(maxWidth: .infinity)
        }
    }

    private var processButton: some View {
        Button {
            viewModel.processImage()
        } label: {
            Text(getProcessButtonText())
                .frame(maxWidth: .infinity)
        }
        .disabled(viewModel.inputImage == nil || viewModel.isProcessing)
    }

    // MARK: - 辅助方法
    private func getInstructionText() -> String {
        if viewModel.subjects.isEmpty {
            return "未检测到主体，或直接提取全部内容"
        } else if viewModel.selectedSubjectIndices.isEmpty {
            return "点击绿色圆点选择要提取的主体，或直接提取全部主体"
        } else {
            return "已选择 \(viewModel.selectedSubjectIndices.count) 个主体，点击「提取选中主体」继续"
        }
    }

    private func getProcessButtonText() -> String {
        if viewModel.selectedSubjectIndices.isEmpty {
            return "提取全部主体"
        } else {
            return "提取选中主体 (\(viewModel.selectedSubjectIndices.count))"
        }
    }

    private var saveButton: some View {
        Button {
            viewModel.saveImage()
        } label: {
            Text("保存图片")
                .frame(maxWidth: .infinity)
        }
        .disabled(viewModel.processedImage == nil)
    }
}

// MARK: - 主体选择指示器组件
struct SubjectIndicator: View {
    let subject: ImageProcess.SubjectInfo
    let isSelected: Bool
    let imageFrame: CGRect
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            ZStack {
                // 外圈
                Circle()
                    .fill(Color.green.opacity(isSelected ? 0.3 : 0.1))
                    .frame(width: 44, height: 44)
                    .overlay(
                        Circle()
                            .stroke(Color.green, lineWidth: isSelected ? 3 : 2)
                    )

                // 内圈
                Circle()
                    .fill(Color.green)
                    .frame(width: isSelected ? 16 : 12, height: isSelected ? 16 : 12)
                    .opacity(isSelected ? 1.0 : 0.6)

                // 选中标记
                if isSelected {
                    Image(systemName: "checkmark")
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(.white)
                }
            }
        }
        .position(
            x: imageFrame.minX + subject.centerPosition.x * imageFrame.width,
            y: imageFrame.minY + subject.centerPosition.y * imageFrame.height
        )
        .scaleEffect(isSelected ? 1.1 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isSelected)
    }
}

// MARK: - 图像分析结果视图
extension ImageProcessView {
    private var imageAnalysisResultView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            HStack {
                Spacer()
                
                if (!viewModel.isAnalyzing && viewModel.analysisResult != nil){
                    Text(viewModel.analysisResult?.Title ?? "识别结果")
                        .font(.title3Brand)
                } else{
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.auxiliaryYellow)
                    
                    Text("图像分析")
                        .font(.headline)
                        .foregroundColor(.textPrimary)
                }

                Spacer()
            }

            if viewModel.isAnalyzing {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("正在分析图像内容...")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }
                .padding(.vertical, Theme.Spacing.sm)
            } else if let result = viewModel.analysisResult {
                // 显示分析结果
                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    // 描述
                    VStack(alignment: .leading, spacing: 4) {
                        Text("描述")
                            .font(.bodyBrand)
                            .foregroundColor(.textPrimary)

                        Text(result.Description)
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                            .padding(.horizontal, Theme.Spacing.sm)
                            .padding(.vertical, Theme.Spacing.xs)
                            .background(Color.cardBackground.opacity(0.5))
                            .cornerRadius(Theme.CornerRadius.sm)
                    }

                    // 标签
                    VStack(alignment: .leading, spacing: 8) {
                        Text("组成标签")
                            .font(.bodyBrand)
                            .foregroundColor(.textPrimary)

                        LazyVGrid(columns: [
                            GridItem(.adaptive(minimum: 80), spacing: 8)
                        ], spacing: 8) {
                            ForEach(result.Tags, id: \.self) { tag in
                                Text(tag)
                                    .font(.captionBrand)
                                    .foregroundColor(.textPrimary)
                                    .padding(.horizontal, Theme.Spacing.sm)
                                    .padding(.vertical, 4)
                                    .background(Color.brandGreen.opacity(0.2))
                                    .cornerRadius(Theme.CornerRadius.sm)
                            }
                        }
                    }

                    // 创建卡片按钮
                    Button(action: createItemCard) {
                        HStack {
                            Image(systemName: "plus.rectangle.on.folder")
                                .foregroundColor(.textPrimary)

                            Text("创建物品卡片")
                                .font(.bodyBrand)
                                .foregroundColor(.textPrimary)
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    .padding(.top, Theme.Spacing.sm)
                }
            } else if let error = viewModel.analysisError {
                // 显示错误信息
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle")
                            .foregroundColor(.error)

                        Text("分析失败")
                            .font(.bodyBrand)
                            .foregroundColor(.error)
                    }
                    
                    Text(error)
                        .font(.captionBrand)
                        .foregroundColor(.error)
                        .padding(.horizontal, Theme.Spacing.sm)
                        .padding(.vertical, Theme.Spacing.xs)
                        .background(Color.cardBackground.opacity(0.5))
                        .cornerRadius(Theme.CornerRadius.sm)
                }
                .padding(.vertical, Theme.Spacing.sm)
            } else {
                // 等待分析状态
                Text("等待图像分析...")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                    .padding(.vertical, Theme.Spacing.sm)
            }
        }
        .padding(Theme.Spacing.md)
        .glassCard()
        .animation(.easeInOut(duration: 0.3), value: viewModel.isAnalyzing)
        .animation(.easeInOut(duration: 0.3), value: viewModel.analysisResult)
    }
}


#Preview {
    ImageProcessView()
}
