//
//  MapView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import SwiftUI
import MapKit
import CoreLocation

/// 为系统类型 CLLocationCoordinate2D 添加 Equatable 扩展
extension CLLocationCoordinate2D: @retroactive Equatable {
    public static func == (lhs: CLLocationCoordinate2D, rhs: CLLocationCoordinate2D) -> Bool {
        lhs.latitude == rhs.latitude && lhs.longitude == rhs.longitude
    }
}

// MARK: - 地图视图组件
struct MapView: View {
    @StateObject private var viewModel: MapViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showLocationPermissionAlert = false
    
    @State private var isAnimating = false

    init() {
        self._viewModel = StateObject(wrappedValue: MapViewModel())
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // MARK: 地图主体
                Map(position: $viewModel.cameraPosition) {
                    // 用户当前位置标记
                    if let userLocation = viewModel.userLocation {
                        Annotation("我在这里", coordinate: userLocation, anchor: .center) {
                            ZStack {
                                // 外圈脉冲效果
                                Circle()
                                    .fill(Color.brandGreen.opacity(0.3))
                                    .frame(width: 40, height: 40)
                                    .scaleEffect(1.5)
                                    .opacity(0.6)
                                    .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: userLocation)
                                // TODO: loop的动画：伴随scale和easeOut效果

                                // 内圈位置标记
                                if let userHeading = viewModel.userHeading{
                                    Image(systemName: "location.fill")
                                        .foregroundColor(.white)
                                        .font(.title2)
                                        .padding(8)
                                        .rotationEffect(Angle(degrees: userHeading))
                                        .background(
                                            Circle()
                                                .fill(Color.brandGreen)
                                                .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                                        )
                                }
                            }
                        }
                    }
                }
                .mapStyle(viewModel.mapStyle)
                .mapControls {
                    MapUserLocationButton()
                    MapCompass()
                    MapScaleView()
                }
                .ignoresSafeArea(.all)

                // MARK: 顶部导航栏
                VStack {
                    HStack {
                        // 关闭按钮
                        Button(action: {
                            dismiss()
                        }) {
                            Image(systemName: "xmark")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding(12)
                                .background(
                                    Circle()
                                        .fill(.ultraThinMaterial)
                                )
                        }
                        .padding(.leading, Theme.Spacing.md)

                        Spacer()

                        // 标题
                        Text("碳迹地图")
                            .font(.title2Brand)
                            .foregroundColor(.white)
                            .padding(.horizontal, Theme.Spacing.lg)
                            .padding(.vertical, Theme.Spacing.sm)
                            .background(
                                Capsule()
                                    .fill(.ultraThinMaterial)
                            )

                        Spacer()

                        // 功能按钮
                        Button(action: {
                            viewModel.toggleMapStyle()
                        }) {
                            Image(systemName: "map")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding(12)
                                .background(
                                    Circle()
                                        .fill(.ultraThinMaterial)
                                )
                        }
                        .padding(.trailing, Theme.Spacing.md)
                    }
                    .padding(.top, Theme.Spacing.md)

                    Spacer()
                }

                // MARK: 底部功能按钮
                VStack {
                    Spacer()

                    HStack {
                        Spacer()

                        VStack(spacing: Theme.Spacing.md) {
                            // 定位按钮
                            Button(action: {
                                viewModel.moveToUserLocation()
                            }) {
                                Image(systemName: "location.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)
                                    .padding(12)
                                    .background(
                                        Circle()
                                            .fill(Color.brandGreen)
                                            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                                    )
                            }

                            // 缩放按钮组
                            VStack(spacing: 4) {
                                Button(action: {
                                    viewModel.zoomToLevel(viewModel.mapRegion.span.latitudeDelta * 0.5)
                                }) {
                                    Image(systemName: "plus")
                                        .font(.title3)
                                        .foregroundColor(.white)
                                        .padding(8)
                                        .background(
                                            Circle()
                                                .fill(.ultraThinMaterial)
                                        )
                                }

                                Button(action: {
                                    viewModel.zoomToLevel(viewModel.mapRegion.span.latitudeDelta * 2.0)
                                }) {
                                    Image(systemName: "minus")
                                        .font(.title3)
                                        .foregroundColor(.white)
                                        .padding(8)
                                        .background(
                                            Circle()
                                                .fill(.ultraThinMaterial)
                                        )
                                }
                            }
                        }
                        .padding(.trailing, Theme.Spacing.md)
                    }
                    .padding(.bottom, Theme.Spacing.xl)
                }
            }
        }
        .navigationBarHidden(true)
        .alert("位置权限", isPresented: $showLocationPermissionAlert) {
            Button("去设置") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage ?? "需要位置权限才能显示您的当前位置")
        }
        .onAppear {
            viewModel.startLocationUpdates()
            isAnimating = true
        }
        .onDisappear {
            viewModel.stopLocationUpdates()
        }
        .onChange(of: viewModel.errorMessage) { _, newValue in
            if newValue != nil {
                showLocationPermissionAlert = true
            }
        }
    }
}

#Preview {
    MapView()
        .stableBackground()
}
