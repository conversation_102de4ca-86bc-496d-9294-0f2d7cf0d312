//
//  CardStore.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import Foundation
import SwiftUI
import CoreLocation

// MARK: - 卡片存储管理
class CardStore: ObservableObject {
    @Published var cards: [ItemCard] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    private let userDefaultsKey = "SavedCards"
    private let itemCardManager = ItemCardManager()
    @MainActor
    private lazy var userItemCardManager = UserItemCardViewModel()

    init() {
        loadCards()
    }
    
    // 保存卡片到云端和本地存储
    func saveCard(
        tags: [String],
        description: String,
        title: String,
        imageData: Data,
        location: String = "",
        latitude: Double? = nil,
        longitude: Double? = nil,
        remark: String? = nil
    ) async -> String? {

        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // 获取当前用户ID
        @AppStorage("currentUserId") var currentUserId: String = ""
        let authorId = currentUserId.isEmpty ? "anonymous" : currentUserId

        // 生成唯一ID
        let cardId = UUID().uuidString

        do {
            // 1. 先上传图片到COS获取URL
            let imageURL = try await ImageShareService.shared.uploadImage(imageData)

            // 2. 保存图片到本地
            let imageFileName = saveImageToDocuments(imageData: imageData, id: cardId)

            // 3. 创建卡片到云端
            let createdCard = try await itemCardManager.createItemCard(
                title: title,
                description: description,
                tags: tags,
                imageFileName: imageFileName,
                imageURL: imageURL,
                authorId: authorId,
                location: location,
                latitude: latitude,
                longitude: longitude
            )

            // 4. 如果作者提供了备注，创建UserItemCard记录
            if let remark = remark, !remark.isEmpty {
                _ = UserItemCard(
                    id: UUID().uuidString,
                    userId: authorId,
                    cardId: createdCard.id,
                    remark: remark,
                    acquiredAt: Date(),
                    isOwner: true,
                    card: createdCard,
                    author: AuthorInfo(userId: authorId, nickname: "我", avatarURL: nil)
                )

                // 这里应该调用API保存UserItemCard，但由于当前API设计，我们先跳过
                // 在实际使用中，应该有专门的API来创建UserItemCard
            }

            // 5. 添加到本地存储
            await MainActor.run {
                cards.append(createdCard)
                saveCards()
                isLoading = false
            }

            print("✅ 卡片创建成功: \(createdCard.id)")
            return createdCard.id

        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
                isLoading = false
            }
            print("❌ 卡片创建失败: \(error)")
            print("❌ 错误详情: \(error.localizedDescription)")
            if let itemCardError = error as? ItemCardError {
                print("❌ ItemCardError 类型: \(itemCardError)")
            }
            return nil
        }
    }

    // 保存卡片数据到UserDefaults（内部方法，公开以便外部调用）
    func saveCards() {
        if let data = try? JSONEncoder().encode(cards) {
            UserDefaults.standard.set(data, forKey: userDefaultsKey)
        }
    }

    // 异步上传图片到COS并更新卡片URL（已废弃，现在在saveCard中直接处理）
    @MainActor
    private func uploadImageToCOS(for card: ItemCard, imageData: Data) async {
        do {
            let imageURL = try await ImageShareService.shared.uploadImage(imageData)

            // 更新卡片的imageURL
            if let index = cards.firstIndex(where: { $0.id == card.id }) {
                let updatedCard = ItemCard(
                    id: card.id,
                    tags: card.tags,
                    description: card.description,
                    title: card.title,
                    imageFileName: card.imageFileName,
                    imageURL: imageURL,
                    createdAt: card.createdAt,
                    authorId: card.authorId,
                    location: card.location,
                    latitude: card.latitude,
                    longitude: card.longitude
                )
                cards[index] = updatedCard
                saveCards()
                print("卡片图片上传成功，URL已更新: \(imageURL)")
            }
        } catch {
            print("图片上传失败: \(error.localizedDescription)")
            // 上传失败时保持本地存储，imageURL为空
        }
    }
    
    // 更新现有卡片
    func updateCard(_ updatedCard: ItemCard) {
        if let index = cards.firstIndex(where: { $0.id == updatedCard.id }) {
            cards[index] = updatedCard
            saveCards()
        }
    }
    
    // 删除卡片
    func deleteCard(_ card: ItemCard) {
        // 删除图像文件
        if !card.imageFileName.isEmpty {
            let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
                .first!.appendingPathComponent(card.imageFileName)
            try? FileManager.default.removeItem(at: url)
        }
        
        // 从数组中移除
        cards.removeAll { $0.id == card.id }
        saveCards()
    }
    
    // 从云端加载用户持有的卡片
    func loadUserCards() async {
        @AppStorage("currentUserId") var currentUserId: String = ""
        guard !currentUserId.isEmpty else {
            await MainActor.run {
                errorMessage = "用户未登录"
            }
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let userItemCards = try await itemCardManager.getUserItemCards(userId: currentUserId)

            await MainActor.run {
                // 提取卡片信息
                self.cards = userItemCards.compactMap { $0.card }
                self.saveCards() // 同步到本地存储
                self.isLoading = false
            }

            print("✅ 成功加载用户卡片: \(userItemCards.count) 张")

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
            print("❌ 加载用户卡片失败: \(error.localizedDescription)")
        }
    }

    // 从 UserDefaults 加载卡片（本地缓存）
    private func loadCards() {
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey),
           let savedCards = try? JSONDecoder().decode([ItemCard].self, from: data) {
            cards = savedCards
        }
    }
    
    // 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    // 同步刷新用户卡片（从云端重新加载）
    func refreshCards() async {
        await loadUserCards()
    }

    // 保存图像到 Documents 目录
    private func saveImageToDocuments(imageData: Data, id: String) -> String {
        let fileName = "\(id).png"
        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            .appendingPathComponent(fileName)
        try? imageData.write(to: url)
        return fileName // 只返回文件名
    }

    // 获取当前位置信息（集成getCurrentLocationInfo）
    func getCurrentLocationInfo() async -> (location: String, latitude: Double?, longitude: Double?) {
        let locationManager = await UserLocationManager()
        let (clLocation, locationString) = await locationManager.getCurrentLocationInfo()

        if let clLocation = clLocation {
            return (locationString, clLocation.coordinate.latitude, clLocation.coordinate.longitude)
        } else {
            return (locationString, nil, nil)
        }
    }
}
