//
//  AuthModels.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import Foundation

// MARK: - 认证请求模型

/// 登录请求模型
struct LoginRequest: Codable {
    let userId: String
    let password: String
}

/// 注册请求模型
struct RegisterRequest: Codable {
    let userId: String
    let password: String
}

// MARK: - 认证响应模型

/// 认证响应基础模型
struct AuthResponse: Codable {
    let message: String
    let userId: String?
}

/// 认证数据模型
struct AuthData: Codable {
    let userId: String
}

// MARK: - 认证错误类型

/// 认证错误枚举
enum AuthError: Error, LocalizedError {
    case invalidCredentials
    case userAlreadyExists
    case networkError
    case serverError
    case invalidResponse
    case tokenExpired
    case userNotFound
    case passwordTooShort
    case userIdTooShort
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidCredentials:
            return "用户ID或密码错误"
        case .userAlreadyExists:
            return "用户ID已存在，请选择其他用户ID"
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .serverError:
            return "服务器错误，请稍后重试"
        case .invalidResponse:
            return "服务器响应格式错误"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .userNotFound:
            return "用户不存在"
        case .passwordTooShort:
            return "密码至少需要6位字符"
        case .userIdTooShort:
            return "用户ID至少需要6位字符"
        case .unknown(let message):
            return message
        }
    }
}

// MARK: - 认证状态

/// 用户认证状态
enum AuthState {
    case notAuthenticated
    case authenticating
    case authenticated(AuthData)
    case error(AuthError)
}

// MARK: - 表单验证

/// 表单验证结果
struct ValidationResult {
    let isValid: Bool
    let errorMessage: String?
    
    static let valid = ValidationResult(isValid: true, errorMessage: nil)
    
    static func invalid(_ message: String) -> ValidationResult {
        return ValidationResult(isValid: false, errorMessage: message)
    }
}

// MARK: - 认证配置

/// 认证配置常量
struct AuthConfig {
    static let baseURL = "https://www.carboncoin.asia/api/"
    static let loginEndpoint = "/auth/login"
    static let registerEndpoint = "/auth/register"
    static let minUserIdLength = 6
    static let minPasswordLength = 6
    static let requestTimeout: TimeInterval = 30.0
}
